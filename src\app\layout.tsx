import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { StructuredData } from "@/components/structured-data";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "<PERSON> - Cybersecurity & Cloud Developer",
  description: "Aspiring Cybersecurity & Cloud Developer exploring secure systems, AI-assisted full stack apps, and n8n automation. Passionate about building smart, scalable, and automated solutions.",
  keywords: ["cybersecurity", "cloud development", "automation", "n8n", "full stack", "AI", "security"],
  authors: [{ name: "<PERSON>", url: "https://github.com/A-Locke" }],
  creator: "<PERSON>",
  publisher: "<PERSON>",
  openGraph: {
    title: "<PERSON>bersecurity & Cloud Developer",
    description: "Aspiring Cybersecurity & Cloud Developer exploring secure systems, AI-assisted full stack apps, and n8n automation.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary",
    title: "Arthur Locke - Cybersecurity & Cloud Developer",
    description: "Aspiring Cybersecurity & Cloud Developer exploring secure systems, AI-assisted full stack apps, and n8n automation.",
    creator: "@IamArthurLocke",
  },
  robots: {
    index: true,
    follow: true,
  },
  verification: {
    other: {
      "business-name": "Arthur Locke",
      "business-id": "14282721",
      "business-address": "Oblouková 1254/6, 101 00, Praha 10 – Vršovice",
      "business-email": "<EMAIL>",
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="business-name" content="Arthur Locke" />
        <meta name="business-id" content="14282721" />
        <meta name="business-address" content="Oblouková 1254/6, 101 00, Praha 10 – Vršovice" />
        <meta name="business-email" content="<EMAIL>" />
        <meta name="contact-email" content="<EMAIL>" />
        <meta name="author" content="Arthur Locke" />
        <meta name="theme-color" content="#ffffff" />
        <meta name="theme-color" content="#0a0a0a" media="(prefers-color-scheme: dark)" />
        <script
          type="text/javascript"
          async
          src="//cdn.credly.com/assets/utilities/embed.js"
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <StructuredData />
        {children}
      </body>
    </html>
  );
}
