"use client"

import { useEffect, useState } from "react"

export function CredlyTest() {
  const [logs, setLogs] = useState<string[]>([])
  const [scriptLoaded, setScriptLoaded] = useState(false)

  const addLog = (message: string) => {
    console.log(`[Credly Test] ${message}`)
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  useEffect(() => {
    addLog("Component mounted")

    // Check if script already exists
    const existingScript = document.querySelector('script[src*="credly.com"]')
    if (existingScript) {
      addLog("Credly script already exists")
      setScriptLoaded(true)
      return
    }

    addLog("Loading Credly script...")
    
    // Load Credly script
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.async = true
    script.src = 'https://cdn.credly.com/assets/utilities/embed.js'

    script.onload = () => {
      addLog("Credly script loaded successfully")
      setScriptLoaded(true)

      // Try multiple approaches to trigger badge rendering
      setTimeout(() => {
        // Check for CrLy object
        if (window.CrLy) {
          addLog("CrLy object found, calling render...")
          window.CrLy.ready(() => {
            addLog("CrLy ready, rendering badges...")
            window.CrLy.badges.render()
          })
        } else {
          addLog("CrLy object not found, trying alternative approaches...")

          // Try to trigger any global badge rendering functions
          if (typeof window.renderCredlyBadges === 'function') {
            addLog("Found renderCredlyBadges function")
            window.renderCredlyBadges()
          }

          // Check what's actually available on window
          const credlyKeys = Object.keys(window).filter(key =>
            key.toLowerCase().includes('cred') || key.toLowerCase().includes('badge')
          )
          addLog(`Available Credly-related keys: ${credlyKeys.join(', ') || 'none'}`)

          // The script might work automatically without needing manual trigger
          addLog("Script loaded, badges should render automatically")
        }
      }, 1000)
    }

    script.onerror = (error) => {
      addLog(`Script failed to load: ${error}`)
    }

    document.head.appendChild(script)
    addLog("Script tag added to head")

  }, [])

  return (
    <div className="p-4 border rounded-lg bg-muted/50">
      <h3 className="font-semibold mb-4">Credly Debug Test</h3>
      
      {/* Debug info */}
      <div className="mb-4 text-sm">
        <p>Script loaded: {scriptLoaded ? "✅ Yes" : "❌ No"}</p>
        <p>CrLy available: {typeof window !== 'undefined' && window.CrLy ? "✅ Yes" : "❌ No"}</p>
      </div>

      {/* Logs */}
      <div className="mb-4">
        <h4 className="font-medium mb-2">Debug Logs:</h4>
        <div className="text-xs bg-background p-2 rounded max-h-32 overflow-y-auto">
          {logs.map((log, i) => (
            <div key={i}>{log}</div>
          ))}
        </div>
      </div>

      {/* Test badge - Method 1: Standard embed */}
      <div className="border-2 border-dashed border-primary/50 p-4 rounded mb-4">
        <p className="text-sm mb-2">Method 1 - Standard Embed:</p>
        <div
          data-iframe-width="150"
          data-iframe-height="270"
          data-share-badge-id="2054310e-e8ff-4b2d-8fa7-ab33a975c32c"
          data-share-badge-host="https://www.credly.com"
          className="w-[150px] h-[270px] bg-gray-100 border"
        />
      </div>

      {/* Test badge - Method 2: Direct iframe */}
      <div className="border-2 border-dashed border-green-500/50 p-4 rounded">
        <p className="text-sm mb-2">Method 2 - Direct iframe (fallback):</p>
        <iframe
          src={`https://www.credly.com/embedded_badge/2054310e-e8ff-4b2d-8fa7-ab33a975c32c`}
          width="150"
          height="270"
          frameBorder="0"
          scrolling="no"
          className="border"
        />
      </div>
    </div>
  )
}

// Extend Window interface for TypeScript
declare global {
  interface Window {
    CrLy: any
  }
}
