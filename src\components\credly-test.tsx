"use client"

import { useEffect, useState } from "react"

export function CredlyTest() {
  const [logs, setLogs] = useState<string[]>([])
  const [scriptLoaded, setScriptLoaded] = useState(false)

  const addLog = (message: string) => {
    console.log(`[Credly Test] ${message}`)
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  useEffect(() => {
    addLog("Component mounted")

    // Check if script already exists
    const existingScript = document.querySelector('script[src*="credly.com"]')
    if (existingScript) {
      addLog("Credly script already exists")
      setScriptLoaded(true)
      return
    }

    addLog("Loading Credly script...")
    
    // Load Credly script
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.async = true
    script.src = 'https://cdn.credly.com/assets/utilities/embed.js'
    
    script.onload = () => {
      addLog("Credly script loaded successfully")
      setScriptLoaded(true)
      
      // Check if CrLy is available
      setTimeout(() => {
        if (window.CrLy) {
          addLog("CrLy object found, calling render...")
          window.CrLy.ready(() => {
            addLog("CrLy ready, rendering badges...")
            window.CrLy.badges.render()
          })
        } else {
          addLog("CrLy object not found")
        }
      }, 500)
    }

    script.onerror = (error) => {
      addLog(`Script failed to load: ${error}`)
    }

    document.head.appendChild(script)
    addLog("Script tag added to head")

  }, [])

  return (
    <div className="p-4 border rounded-lg bg-muted/50">
      <h3 className="font-semibold mb-4">Credly Debug Test</h3>
      
      {/* Debug info */}
      <div className="mb-4 text-sm">
        <p>Script loaded: {scriptLoaded ? "✅ Yes" : "❌ No"}</p>
        <p>CrLy available: {typeof window !== 'undefined' && window.CrLy ? "✅ Yes" : "❌ No"}</p>
      </div>

      {/* Logs */}
      <div className="mb-4">
        <h4 className="font-medium mb-2">Debug Logs:</h4>
        <div className="text-xs bg-background p-2 rounded max-h-32 overflow-y-auto">
          {logs.map((log, i) => (
            <div key={i}>{log}</div>
          ))}
        </div>
      </div>

      {/* Test badge */}
      <div className="border-2 border-dashed border-primary/50 p-4 rounded">
        <p className="text-sm mb-2">Test Badge (should appear below if working):</p>
        <div 
          data-iframe-width="150" 
          data-iframe-height="270" 
          data-share-badge-id="2054310e-e8ff-4b2d-8fa7-ab33a975c32c" 
          data-share-badge-host="https://www.credly.com"
          className="w-[150px] h-[270px] bg-gray-100 border"
        />
      </div>
    </div>
  )
}

// Extend Window interface for TypeScript
declare global {
  interface Window {
    CrLy: any
  }
}
